<script lang="ts" setup>
import { ref, reactive, onMounted, useTemplateRef } from "vue";
import { ElMessage } from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type { RoleInfo, RoleSearchParams } from "@/common/apis/roles/type";
import { Search, Refresh } from "@element-plus/icons-vue";
import { getRoleListApi } from "@/common/apis/roles";

defineOptions({
  // 命名当前组件
  name: "RoleManagement",
});

// 表格实例引用
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 搜索表单数据
const searchForm = reactive<RoleSearchParams>({
  name: "",
  code: "",
  status: "",
});

// 状态选项
const statusOptions = [
  { label: "全部", value: "" },
  { label: "启用", value: 1 },
  { label: "禁用", value: 0 },
];

// 获取状态标签类型
const getStatusTagType = (status: number) => {
  return status === 1 ? "success" : "danger";
};

// 获取状态文本
const getStatusText = (status: number) => {
  return status === 1 ? "启用" : "禁用";
};

// 表格配置
const xGridOpt: VxeGridProps = reactive({
  loading: false,
  autoResize: true,
  /** 分页配置项 */
  pagerConfig: {
    align: "right",
  },
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["name"].includes(column.field),
  },
  /** 列配置 */
  columns: [
    {
      type: "checkbox",
      width: 50,
    },
    {
      type: "seq",
      width: 70,
      title: "序号",
    },
    {
      field: "name",
      title: "角色名称",
      minWidth: 120,
    },
    {
      field: "code",
      title: "角色编码",
      minWidth: 120,
    },
    {
      field: "status",
      title: "状态",
      width: 100,
      slots: { default: "status-column" },
    },
    {
      field: "createTime",
      title: "创建时间",
      width: 180,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : "";
      },
    },
    {
      field: "updateTime",
      title: "更新时间",
      width: 180,
      formatter: ({ cellValue }: { cellValue: string }) => {
        return cellValue ? new Date(cellValue).toLocaleString() : "";
      },
    },
    {
      field: "remark",
      title: "备注",
      minWidth: 150,
      showOverflow: "tooltip",
    },
    {
      title: "操作",
      width: 150,
      fixed: "right",
      slots: { default: "action-column" },
    },
  ],
  /** 数据代理配置项（基于 Promise API） */
  proxyConfig: {
    /** 启用动态序号代理 */
    seq: true,
    /** 是否自动加载，默认为 true */
    autoLoad: true,
    props: {
      total: "total",
    },
    ajax: {
      query: () => {
        xGridOpt.loading = true;
        return new Promise((resolve) => {
          loadRoleData()
            .then((res) => {
              xGridOpt.loading = false;
              resolve(res);
            })
            .catch(() => {
              xGridOpt.loading = false;
              resolve({ total: 0, result: [] });
            });
        });
      },
    },
  },
});

// 角色数据加载函数
const loadRoleData = async () => {
  try {
    const params: RoleSearchParams = {
      name: searchForm.name || undefined,
      code: searchForm.code || undefined,
      status: searchForm.status || undefined,
    };

    const response = await getRoleListApi(params);
    
    return {
      total: response.data.data.length,
      result: response.data.data,
    };
  } catch (error) {
    console.error("获取角色数据失败:", error);
    ElMessage.error("获取角色数据失败，请稍后重试");
    return {
      total: 0,
      result: [],
    };
  }
};

// 查询数据
const handleQuery = () => {
  xGridDom.value?.commitProxy("query");
};

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    name: "",
    code: "",
    status: "",
  });
  handleQuery();
};

// 新增角色
const handleAdd = () => {
  ElMessage.info("新增角色功能待实现");
};

// 批量删除
const handleBatchDelete = () => {
  const selectRecords = xGridDom.value?.getCheckboxRecords();
  if (!selectRecords || selectRecords.length === 0) {
    ElMessage.warning("请选择要删除的角色");
    return;
  }
  ElMessage.info("批量删除功能待实现");
};

// 编辑角色
const handleEdit = (row: RoleInfo) => {
  ElMessage.info(`编辑角色：${row.name}`);
};

// 删除角色
const handleDelete = (row: RoleInfo) => {
  ElMessage.info(`删除角色：${row.name}`);
};

onMounted(() => {
  // 组件挂载后可以进行一些初始化操作
});
</script>

<template>
  <div class="role-management">
    <!-- 搜索区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <el-form :model="searchForm" inline>
          <el-form-item label="角色名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入角色名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="角色编码">
            <el-input
              v-model="searchForm.code"
              placeholder="请输入角色编码"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option
                v-for="option in statusOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleQuery">
              搜索
            </el-button>
            <el-button :icon="Refresh" @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <el-card class="table-card">
        <vxe-grid ref="xGridDom" v-bind="xGridOpt">
          <!-- 工具栏按钮 -->
          <template #toolbar-btns>
            <vxe-button status="primary" icon="vxe-icon-add" @click="handleAdd">
              新增角色
            </vxe-button>
            <vxe-button status="danger" icon="vxe-icon-delete" @click="handleBatchDelete">
              批量删除
            </vxe-button>
          </template>

          <!-- 状态列 -->
          <template #status-column="{ row, column }">
            <el-tag
              :type="getStatusTagType(row[column.field])"
              effect="light"
              size="small"
            >
              {{ getStatusText(row[column.field]) }}
            </el-tag>
          </template>

          <!-- 操作列 -->
          <template #action-column="{ row }">
            <el-button
              link
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </vxe-grid>
      </el-card>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.role-management {
  padding: 20px;
  
  .search-section {
    margin-bottom: 20px;
    
    .search-card {
      :deep(.el-card__body) {
        padding: 20px;
      }
    }
  }
  
  .table-section {
    .table-card {
      :deep(.el-card__body) {
        padding: 0;
      }
    }
  }
}
</style>
